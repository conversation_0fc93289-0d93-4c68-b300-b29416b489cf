#!/bin/bash
set -e

echo "🔧 Frontend/Vercel Install Script - Forcing pnpm usage"

# Always disable <PERSON><PERSON> hooks during CI/builds to avoid dependency prepare/postinstall failures
export HUSKY=0
export HUSKY_SKIP_INSTALL=1
echo "🚫 <PERSON>sky disabled for CI (HUSKY=$HUSKY, HUSKY_SKIP_INSTALL=$HUSKY_SKIP_INSTALL)"

echo "📦 Enabling corepack..."
corepack enable

echo "📦 Preparing pnpm@9.15.9..."
corepack prepare pnpm@9.15.9 --activate

echo "🔍 Verifying pnpm installation..."
which pnpm || true
pnpm --version

echo "📦 Installing dependencies with pnpm (ignoring lifecycle scripts)..."
pnpm install --ignore-scripts

echo "✅ Dependencies installed successfully with pnpm"

