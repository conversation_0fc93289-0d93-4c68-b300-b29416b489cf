{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "isolatedModules": true, "jsx": "preserve", "jsxImportSource": "react", "incremental": true, "baseUrl": ".", "ignoreDeprecations": "5.0", "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/hooks/*": ["./src/hooks/*"], "@/types/*": ["./src/types/*"], "@/app/*": ["./src/app/*"], "@/contexts/*": ["./src/contexts/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "next-env.d.ts", ".next/types/**/*.ts", "src/types/**/*.d.ts"], "exclude": ["node_modules", ".next", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/*.cy.ts", "**/*.cy.tsx", "**/__tests__/**/*", "**/__mocks__/**/*", "src/__tests__/**/*", "src/__mocks__/**/*", "src/**/__tests__/**/*", "src/**/__mocks__/**/*", "src/**/test/**/*", "src/**/tests/**/*", "src/test/**/*", "cypress/**/*", "tests/**/*", "__tests__/**/*", "__mocks__/**/*", "vitest.setup.ts", "vitest.config.ts", "vitest.*.config.ts"]}