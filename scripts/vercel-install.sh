#!/bin/bash
set -e

echo "🔧 Custom Vercel Install Script - Forcing pnpm usage"

# Check if we're in Vercel environment
if [ "$VERCEL" = "1" ]; then
    echo "✅ Detected Vercel environment"

    # Disable <PERSON><PERSON> hooks during CI/builds to avoid dependency prepare/postinstall failures
    export HUSKY=0
    export HUSKY_SKIP_INSTALL=1
    echo "🚫 <PERSON><PERSON> disabled for CI (HUSKY=$HUSKY, HUSKY_SKIP_INSTALL=$HUSKY_SKIP_INSTALL)"

    # Restore pnpm-lock.yaml if it was hidden
    if [ -f "pnpm-lock.yaml.hidden" ]; then
        echo "📦 Restoring pnpm-lock.yaml..."
        mv pnpm-lock.yaml.hidden pnpm-lock.yaml
    fi

    # Enable corepack and install pnpm
    echo "📦 Enabling corepack..."
    corepack enable

    echo "📦 Preparing pnpm@9.15.9..."
    corepack prepare pnpm@9.15.9 --activate

    # Verify pnpm is available
    echo "🔍 Verifying pnpm installation..."
    which pnpm
    pnpm --version

    # Install dependencies with pnpm
    echo "📦 Installing dependencies with pnpm (ignoring lifecycle scripts)..."
    # Ignore lifecycle scripts to avoid third-party packages running husky or similar during install
    pnpm install --ignore-scripts

    echo "✅ Dependencies installed successfully with pnpm"
else
    echo "🏠 Local environment detected, using regular pnpm install"
    pnpm install
fi
