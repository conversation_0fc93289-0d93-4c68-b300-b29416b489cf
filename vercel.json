{"installCommand": "export HUSKY=0 && export HUSKY_SKIP_INSTALL=1 && corepack enable && corepack prepare pnpm@9.15.9 --activate && pnpm --version && pnpm install --ignore-scripts", "buildCommand": "cd frontend && pnpm build", "outputDirectory": "frontend/.next", "framework": null, "functions": {"frontend/src/app/api/**/*.ts": {"maxDuration": 30}}, "env": {"ENABLE_EXPERIMENTAL_COREPACK": "1", "COREPACK_ENABLE_STRICT": "1", "VERCEL": "1", "HUSKY": "0", "HUSKY_SKIP_INSTALL": "1"}}